[useFCMMessageRouter] FCM message received, app state: active
useFCMMessageRouter.ts:43 [useFCMMessageRouter] Processing foreground FCM message: {info: '{"callerInfo":{"name":"R17 C","token":"c0fmPq0aQyC6-AbtICnA5i:APA91bHGHglqO5cH7les2jNIaiaevezowB_izibajl1Xadcz7l9iLujO-9U53DuzwSTESCQ1LIU5gL79ZjyFMvrovgIBObK3_mTOnB6OLdHocTcELR06Cx8"},"videoSDKInfo":{"meetingId":"03c1-mzhg-a71r","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************.4jTc988wh830XKhtHsS3Rm4N4DVtYiVhwmX1FdX_7h4","callType":"voice"},"type":"CALL_INITIATED","uuid":"dffe0741-d37d-4934-bb68-97eb19b4983d"}'}
LogUtils.ts:44 [FCMMessageRouter] Routing foreground message: {data: {…}, notification: undefined}
LogUtils.ts:44 [FCMMessageRouter] Detected message type: CALL_INITIATED
FCMMessageRouter.ts:137 [FCMMessageRouter] Routing to enhanced call handler...
CallFCMHandler.ts:44 [CallFCMHandler] Processing call message: {context: 'foreground', messageType: 'CALL_INITIATED', data: {…}}
CallFCMHandler.ts:91 [CallFCMHandler] Handling incoming call with UI coordinator: {sessionId: 'dffe0741-d37d-4934-bb68-97eb19b4983d', meetingId: '03c1-mzhg-a71r', token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************.4jTc988wh830XKhtHsS3Rm4N4DVtYiVhwmX1FdX_7h4', callerName: 'R17 C', callerId: undefined, callType: 'voice', uuid: 'dffe0741-d37d-4934-bb68-97eb19b4983d'}
CallUICoordinator.ts:55 [CallUICoordinator] Initializing...
CallKeepService.ts:87 [CallKeepService] 🚫 CallKeep disabled for Vivo device to prevent blank screen
CallKeepService.ts:88 [CallKeepService] 💡 App will use custom call UI instead of native CallKeep
ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
CallUICoordinator.ts:60 [CallUICoordinator] Initialization complete
CallUICoordinator.ts:71 [CallUICoordinator] Showing incoming call: {sessionId: 'dffe0741-d37d-4934-bb68-97eb19b4983d', callerName: 'R17 C', callType: 'voice', meetingId: '03c1-mzhg-a71r', token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************.4jTc988wh830XKhtHsS3Rm4N4DVtYiVhwmX1FdX_7h4'}
CallUICoordinator.ts:103 [CallUICoordinator] CallKeep not available, using custom UI
CallUICoordinator.ts:174 [CallUICoordinator] Displaying custom UI
ProductionLogger.ts:117 [CALL:NotificationService] Showing incoming call notification {sessionId: 'dffe0741-d37d-4934-bb68-97eb19b4983d', callerName: 'R17 C', type: 'video', isConcurrentCall: '03c1-mzhg-a71r'}
NotificationPersistenceService.ts:36 [NotificationPersistence] Initializing service
NotificationPersistenceService.ts:121 [NotificationPersistence] Processing pending calls
NotificationPersistenceService.ts:56 [NotificationPersistence] Adding pending call: dffe0741-d37d-4934-bb68-97eb19b4983d
NotificationPersistenceService.ts:145 [NotificationPersistence] Cleaning up expired calls
ProductionLogger.ts:117 [CALL:NotificationService] Enhanced notification channels created undefined
ProductionLogger.ts:117 [CALL:NotificationService] Native incoming call triggered undefined
ProductionLogger.ts:117 [CALL:NotificationService] Notifee notification displayed successfully undefined
NotificationPersistenceService.ts:84 [NotificationPersistence] Removing pending call: dffe0741-d37d-4934-bb68-97eb19b4983d
CallUICoordinator.ts:191 [CallUICoordinator] Custom UI displayed successfully
CallFCMHandler.ts:119 [CallFCMHandler] Call UI displayed successfully via coordinator
FCMMessageRouter.ts:161 [FCMMessageRouter] Enhanced call message processed successfully
useFCMMessageRouter.ts:45 [useFCMMessageRouter] Foreground message routed successfully
AppOpenAdManager.ts:170 App state changed: active -> background
AppOpenAdManager.ts:210 App went to background, resetting session flag
ReliableCallManager.ts:261 [ReliableCallManager] App state changed to: background
console.js:654 App has gone to is inactive. Stop timer.
useFCMMessageRouter.ts:58 [useFCMMessageRouter] Notification action pressed: answer
ReliableCallManager.ts:398 [ReliableCallManager] Notification action pressed: answer
ReliableCallManager.ts:399 [ReliableCallManager] Current session: undefined
ReliableCallManager.ts:400 [ReliableCallManager] Notification sessionId: dffe0741-d37d-4934-bb68-97eb19b4983d
ReliableCallManager.ts:404 [ReliableCallManager] Notification action for different session, ignoring
anonymous @ console.js:654
overrideMethod @ backend.js:17042
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:84
?anon_0_ @ ReliableCallManager.ts:404
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
handleNotificationAction @ ReliableCallManager.ts:390
?anon_0_ @ ReliableCallManager.ts:239
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
anonymous @ asyncToGenerator.js:22
anonymous @ asyncToGenerator.js:14
anonymous @ ReliableCallManager.ts:241
anonymous @ NotifeeApiModule.js:349
emit @ EventEmitter.js:126
anonymous @ NotifeeNativeModule.js:24
emit @ EventEmitter.js:126
anonymous @ RCTDeviceEventEmitter.js:14
emit @ RCTDeviceEventEmitter.js:33
Show 19 more frames
Show less
AppOpenAdManager.ts:170 App state changed: background -> active
AppOpenAdManager.ts:183 [AppOpenAdManager] Suppressing ad because a call is active.
ReliableCallManager.ts:261 [ReliableCallManager] App state changed to: active
console.js:654 App has come to foreground. Start timer.
CallSignalingService.ts:34 [CallSignalingService] Disabled by configuration - skipping FCM listener setup
ProductionLogger.ts:117 [CALL:CallController] Accepting call {sessionId: 'dffe0741-d37d-4934-bb68-97eb19b4983d'}
ProductionLogger.ts:117 [CALL:CallController] Validating call permissions for incoming call... undefined
PermissionManagerService.ts:232 [PermissionManager] Requesting call permissions... {includeCamera: false}
AppOpenAdManager.ts:170 App state changed: background -> background
AppOpenAdManager.ts:210 App went to background, resetting session flag
ReliableCallManager.ts:261 [ReliableCallManager] App state changed to: background
console.js:654 App has gone to is inactive. Stop timer.
AppOpenAdManager.ts:170 App state changed: background -> active
AppOpenAdManager.ts:183 [AppOpenAdManager] Suppressing ad because a call is active.
ReliableCallManager.ts:261 [ReliableCallManager] App state changed to: active
console.js:654 App has come to foreground. Start timer.
PermissionManagerService.ts:268 [PermissionManager] Call permissions result: {microphone: true, camera: false}
ProductionLogger.ts:117 [CALL:CallController] Call permissions validated for accepting call {camera: false, microphone: true}
ProductionLogger.ts:117 [CALL:NotificationService] Hiding notification {id: 'dffe0741-d37d-4934-bb68-97eb19b4983d'}
ProductionLogger.ts:117 [CALL:CallController] Status changed: ringing -> connecting undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Already initialized and WebSocket ready undefined
ProductionLogger.ts:117 [CALL:NotificationService] Native call ended undefined
ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
MediaService.ts:77 [MediaService] Ensuring clean state for new meeting
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Starting comprehensive service reset undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Clearing component instance tracking undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Service reset complete undefined
ProductionLogger.ts:117 [CALL:NotificationService] Cancelled notification {id: 'dffe0741-d37d-4934-bb68-97eb19b4983d'}
ProductionLogger.ts:117 [CALL:NotificationService] Stopped foreground service undefined
ProductionLogger.ts:117 [CALL:NotificationService] Cleared all notifications as fallback undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Starting comprehensive service reset undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Clearing component instance tracking undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Service reset complete undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Starting VideoSDK initialization with config: {region: 'us001', websocketConfig: {…}}
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Registering VideoSDK... undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] WebSocket connection attempt 1/3 undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Validating VideoSDK connection... undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] WebSocket connection established on attempt 1 undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] VideoSDK initialization complete with WebSocket ready undefined
MediaService.ts:38 [MediaService] Joining meeting: {meetingId: '03c1-mzhg-a71r', name: 'R17 C', type: 'video'}
console.js:654 ProgressBarAndroid has been extracted from react-native core and will be removed in a future release. It can now be installed and imported from '@react-native-community/progress-bar-android' instead of 'react-native'. See https://github.com/react-native-progress-view/progress-bar-android
anonymous @ console.js:654
overrideMethod @ backend.js:17042
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:84
warnOnce @ warnOnce.js:27
get ProgressBarAndroid @ index.js:81
metroImportAll @ require.js:143
importAll @ asyncRequire.js:18
Show 9 more frames
Show less
console.js:654 Clipboard has been extracted from react-native core and will be removed in a future release. It can now be installed and imported from '@react-native-clipboard/clipboard' instead of 'react-native'. See https://github.com/react-native-clipboard/clipboard
anonymous @ console.js:654
overrideMethod @ backend.js:17042
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:84
warnOnce @ warnOnce.js:27
get Clipboard @ index.js:168
metroImportAll @ require.js:143
importAll @ asyncRequire.js:18
Show 9 more frames
Show less
console.js:654 PushNotificationIOS has been extracted from react-native core and will be removed in a future release. It can now be installed and imported from '@react-native-community/push-notification-ios' instead of 'react-native'. See https://github.com/react-native-push-notification/ios
anonymous @ console.js:654
overrideMethod @ backend.js:17042
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:84
warnOnce @ warnOnce.js:27
get PushNotificationIOS @ index.js:232
metroImportAll @ require.js:143
importAll @ asyncRequire.js:18
Show 9 more frames
Show less
MediaService.ts:55 [MediaService] Using regular navigation
NavigationService.ts:69 [NavigationService] Navigating to Meeting screen with params: {meetingId: '03c1-mzhg-a71r', callType: 'video', displayName: 'R17 C', localParticipantId: 'not_provided'}
console.js:654 Looks like you're passing an inline function for 'component' prop for the screen 'Profile' (e.g. component={() => <SomeComponent />}). Passing an inline function will cause the component state to be lost on re-render and cause perf issues since it's re-created every render. You can pass the function as children to 'Screen' instead to achieve the desired behaviour. Error Component Stack:
    at NativeStackNavigator (createNativeStackNavigator.js:8:5)
    at MainNavigator (MainNavigator.tsx:173:25)
    at StaticContainer (StaticContainer.js:9:15)
    at EnsureSingleNavigator (EnsureSingleNavigator.js:12:11)
    at SceneView (SceneView.js:15:9)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (DebugContainer.tsx:25:49)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (createAnimatedComponent.js:67:57)
    at Suspender (index.tsx:6:9)
    at Suspense (<anonymous>)
    at Freeze (index.tsx:24:32)
    at DelayedFreeze (DelayedFreeze.tsx:11:32)
    at InnerScreen (Screen.tsx:62:34)
    at Screen (Screen.tsx:290:41)
    at ScreenStackItem (ScreenStackItem.tsx:30:13)
    at SceneView (NativeStackView.native.js:22:8)
    at RNSScreenStack (<anonymous>)
    at anonymous (contexts.tsx:5:63)
    at ScreenStack (ScreenStack.tsx:60:12)
    at RCTView (<anonymous>)
    at View (View.js:32:34)
    at SafeAreaProviderCompat (SafeAreaProviderCompat.js:30:11)
    at NativeStackView (NativeStackView.native.js:326:8)
    at PreventRemoveProvider (PreventRemoveProvider.js:31:11)
    at NavigationContent (useComponent.js:6:9)
    at anonymous (useComponent.js:22:13)
    at NativeStackNavigator (createNativeStackNavigator.js:8:5)
    at ThemeProvider (ThemeProvider.js:7:8)
    at EnsureSingleNavigator (EnsureSingleNavigator.js:12:11)
    at BaseNavigationContainer (BaseNavigationContainer.js:72:15)
    at NavigationContainerInner (NavigationContainer.js:42:9)
    at NavigationErrorBoundary (NavigationErrorBoundary.tsx:19:29)
    at RNCSafeAreaView (<anonymous>)
    at anonymous (SafeAreaView.tsx:24:11)
    at CssInterop.unknown (api.js:32:48)
    at UltraFastLoader (UltraFastLoader.tsx:67:27)
    at AppNavigator (App.tsx:106:59)
    at RNGestureHandlerRootView (<anonymous>)
    at GestureHandlerRootView (GestureHandlerRootView.android.tsx:12:8)
    at SidebarProvider (SidebarContext.tsx:18:11)
    at TabNavigatorProvider (TabNavigatorContext.tsx:16:84)
    at ShortsProvider (ShortsContext.tsx:24:73)
    at DataProvider (DataProvider.tsx:16:69)
    at ContentCreatorPremiumProvider (ContentCreatorPremiumContext.tsx:35:103)
    at WalletProvider (WalletContext.tsx:36:72)
    at FCMChatProvider (FCMChatContext.tsx:59:75)
    at UserDataProvider (UserDataContext.tsx:39:77)
    at QueryClientProvider (QueryClientProvider.js:20:9)
    at EnhancedQueryProvider (QueryProvider.tsx:60:79)
    at AuthProvider (AuthContext.tsx:89:11)
    at ThemeProvider (ThemeContext.tsx:22:11)
    at RCTView (<anonymous>)
    at View (View.js:32:34)
    at CssInterop.View (api.js:32:48)
    at KeyboardAvoiderProvider (KeyboardAvoiderProvider.tsx:7:13)
    at SafeAreaEnv (react-native-safe-area-context.native.js:17:36)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (SafeAreaContext.tsx:35:11)
    at SafeAreaProviderShim (react-native-safe-area-context.native.js:41:52)
    at RNGestureHandlerRootView (<anonymous>)
    at GestureHandlerRootView (GestureHandlerRootView.android.tsx:12:8)
    at AppErrorBoundary (AppErrorBoundary.tsx:19:29)
    at App (App.tsx:308:48)
    at RCTView (<anonymous>)
    at View (View.js:32:34)
    at CssInterop.View (api.js:32:48)
    at RCTView (<anonymous>)
    at View (View.js:32:34)
    at CssInterop.View (api.js:32:48)
    at AppContainer (AppContainer-dev.js:88:11)
    at Adtip(RootComponent) (getCachedComponentWithDebugName.js:26:42)
anonymous @ console.js:654
overrideMethod @ backend.js:17042
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:84
anonymous @ useNavigationBuilder.js:123
getRouteConfigsFromChildren @ useNavigationBuilder.js:92
useNavigationBuilder @ useNavigationBuilder.js:155
NativeStackNavigator @ createNativeStackNavigator.js:24
reactStackBottomFrame @ ReactFabric-dev.js:14768
renderWithHooks @ ReactFabric-dev.js:4581
updateFunctionComponent @ ReactFabric-dev.js:6959
beginWork @ ReactFabric-dev.js:8215
runWithFiberInDEV @ ReactFabric-dev.js:571
performUnitOfWork @ ReactFabric-dev.js:12184
workLoopSync @ ReactFabric-dev.js:12010
renderRootSync @ ReactFabric-dev.js:11990
performWorkOnRoot @ ReactFabric-dev.js:11478
performSyncWorkOnRoot @ ReactFabric-dev.js:2822
flushSyncWorkAcrossRoots_impl @ ReactFabric-dev.js:2689
processRootScheduleInMicrotask @ ReactFabric-dev.js:2717
anonymous @ ReactFabric-dev.js:2839
Show 22 more frames
Show less
ProductionLogger.ts:117 [CALL:MeetingScreenSimple] Component not active or not initialized, returning null undefined
ProductionLogger.ts:126 [VideoSDK:MeetingScreenSimple] Initializing VideoSDK before MeetingProvider creation undefined
ProductionLogger.ts:126 [VideoSDK:MeetingScreenSimple] VideoSDK is ready, setting videoSDKReady to true undefined
ProductionLogger.ts:117 [CALL:MeetingScreenSimple] State changed {sessionId: 'dffe0741-d37d-4934-bb68-97eb19b4983d', status: 'connecting', sessionIsValid: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************.4jTc988wh830XKhtHsS3Rm4N4DVtYiVhwmX1FdX_7h4', callIsActive: true, videoSDKReady: false, isComponentActive: false, hasInitialized: false}
ProductionLogger.ts:117 [CALL:[MeetingScreenSimple] Component mounted with ID:] 6jkpdnyta Session:
ProductionLogger.ts:117 [CALL:[MeetingScreenSimple] Component activated and registered for session:] dffe0741-d37d-4934-bb68-97eb19b4983d Component ID:
ProductionLogger.ts:51 [DEBUG:NavigationAnalytics] Screen view tracked: Main
console.js:654 Looks like you're passing an inline function for 'component' prop for the screen 'Profile' (e.g. component={() => <SomeComponent />}). Passing an inline function will cause the component state to be lost on re-render and cause perf issues since it's re-created every render. You can pass the function as children to 'Screen' instead to achieve the desired behaviour. Error Component Stack:
    at NativeStackNavigator (createNativeStackNavigator.js:8:5)
    at MainNavigator (MainNavigator.tsx:173:25)
    at StaticContainer (StaticContainer.js:9:15)
    at EnsureSingleNavigator (EnsureSingleNavigator.js:12:11)
    at SceneView (SceneView.js:15:9)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (DebugContainer.tsx:25:49)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (createAnimatedComponent.js:67:57)
    at Suspender (index.tsx:6:9)
    at Suspense (<anonymous>)
    at Freeze (index.tsx:24:32)
    at DelayedFreeze (DelayedFreeze.tsx:11:32)
    at InnerScreen (Screen.tsx:62:34)
    at Screen (Screen.tsx:290:41)
    at ScreenStackItem (ScreenStackItem.tsx:30:13)
    at SceneView (NativeStackView.native.js:22:8)
    at RNSScreenStack (<anonymous>)
    at anonymous (contexts.tsx:5:63)
    at ScreenStack (ScreenStack.tsx:60:12)
    at RCTView (<anonymous>)
    at View (View.js:32:34)
    at SafeAreaProviderCompat (SafeAreaProviderCompat.js:30:11)
    at NativeStackView (NativeStackView.native.js:326:8)
    at PreventRemoveProvider (PreventRemoveProvider.js:31:11)
    at NavigationContent (useComponent.js:6:9)
    at anonymous (useComponent.js:22:13)
    at NativeStackNavigator (createNativeStackNavigator.js:8:5)
    at ThemeProvider (ThemeProvider.js:7:8)
    at EnsureSingleNavigator (EnsureSingleNavigator.js:12:11)
    at BaseNavigationContainer (BaseNavigationContainer.js:72:15)
    at NavigationContainerInner (NavigationContainer.js:42:9)
    at NavigationErrorBoundary (NavigationErrorBoundary.tsx:19:29)
    at RNCSafeAreaView (<anonymous>)
    at anonymous (SafeAreaView.tsx:24:11)
    at CssInterop.unknown (api.js:32:48)
    at UltraFastLoader (UltraFastLoader.tsx:67:27)
    at AppNavigator (App.tsx:106:59)
    at RNGestureHandlerRootView (<anonymous>)
    at GestureHandlerRootView (GestureHandlerRootView.android.tsx:12:8)
    at SidebarProvider (SidebarContext.tsx:18:11)
    at TabNavigatorProvider (TabNavigatorContext.tsx:16:84)
    at ShortsProvider (ShortsContext.tsx:24:73)
    at DataProvider (DataProvider.tsx:16:69)
    at ContentCreatorPremiumProvider (ContentCreatorPremiumContext.tsx:35:103)
    at WalletProvider (WalletContext.tsx:36:72)
    at FCMChatProvider (FCMChatContext.tsx:59:75)
    at UserDataProvider (UserDataContext.tsx:39:77)
    at QueryClientProvider (QueryClientProvider.js:20:9)
    at EnhancedQueryProvider (QueryProvider.tsx:60:79)
    at AuthProvider (AuthContext.tsx:89:11)
    at ThemeProvider (ThemeContext.tsx:22:11)
    at RCTView (<anonymous>)
    at View (View.js:32:34)
    at CssInterop.View (api.js:32:48)
    at KeyboardAvoiderProvider (KeyboardAvoiderProvider.tsx:7:13)
    at SafeAreaEnv (react-native-safe-area-context.native.js:17:36)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (SafeAreaContext.tsx:35:11)
    at SafeAreaProviderShim (react-native-safe-area-context.native.js:41:52)
    at RNGestureHandlerRootView (<anonymous>)
    at GestureHandlerRootView (GestureHandlerRootView.android.tsx:12:8)
    at AppErrorBoundary (AppErrorBoundary.tsx:19:29)
    at App (App.tsx:308:48)
    at RCTView (<anonymous>)
    at View (View.js:32:34)
    at CssInterop.View (api.js:32:48)
    at RCTView (<anonymous>)
    at View (View.js:32:34)
    at CssInterop.View (api.js:32:48)
    at AppContainer (AppContainer-dev.js:88:11)
    at Adtip(RootComponent) (getCachedComponentWithDebugName.js:26:42)
anonymous @ console.js:654
overrideMethod @ backend.js:17042
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:84
anonymous @ useNavigationBuilder.js:123
getRouteConfigsFromChildren @ useNavigationBuilder.js:92
useNavigationBuilder @ useNavigationBuilder.js:155
NativeStackNavigator @ createNativeStackNavigator.js:24
reactStackBottomFrame @ ReactFabric-dev.js:14768
renderWithHooks @ ReactFabric-dev.js:4581
updateFunctionComponent @ ReactFabric-dev.js:6959
beginWork @ ReactFabric-dev.js:8215
runWithFiberInDEV @ ReactFabric-dev.js:571
performUnitOfWork @ ReactFabric-dev.js:12184
workLoopSync @ ReactFabric-dev.js:12010
renderRootSync @ ReactFabric-dev.js:11990
performWorkOnRoot @ ReactFabric-dev.js:11478
performSyncWorkOnRoot @ ReactFabric-dev.js:2822
flushSyncWorkAcrossRoots_impl @ ReactFabric-dev.js:2689
processRootScheduleInMicrotask @ ReactFabric-dev.js:2717
anonymous @ ReactFabric-dev.js:2839
Show 22 more frames
Show less
ProductionLogger.ts:117 [CALL:MeetingScreenSimple] Rendering active component for session {sessionId: 'dffe0741-d37d-4934-bb68-97eb19b4983d', componentId: '6jkpdnyta'}
ProductionLogger.ts:117 [CALL:MeetingScreenSimple] MeetingProvider config {token: 'present', config: {…}}
ProductionLogger.ts:117 [CALL:MeetingScreenSimple] State changed {sessionId: 'dffe0741-d37d-4934-bb68-97eb19b4983d', status: 'connecting', sessionIsValid: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************.4jTc988wh830XKhtHsS3Rm4N4DVtYiVhwmX1FdX_7h4', callIsActive: true, videoSDKReady: true, isComponentActive: true, hasInitialized: true}
ProductionLogger.ts:51 [DEBUG:NavigationAnalytics] Screen view tracked: Main
CallSignalingService.ts:168 [CallSignalingService] Sending CALL_ACCEPT signal: {recipientId: 'unknown', sessionId: 'dffe0741-d37d-4934-bb68-97eb19b4983d'}
ApiService.ts:1553 [ApiService] Sending call signal to recipient: unknown {type: 'CALL_ACCEPT', sessionId: 'dffe0741-d37d-4934-bb68-97eb19b4983d', callType: 'voice', meetingId: 'accepted', token: 'accepted'}
ApiService.ts:1071 [ApiService] Getting FCM token for single user: unknown
2ProductionLogger.ts:51 [DEBUG:NavigationPersistence] Navigation state saved successfully
ApiService.ts:246 Request to protected endpoint: /api/get-fcm-token/unknown. Attempting to add Authorization header.
ProductionLogger.ts:117 [CALL:[MeetingContent] Component rendering...] undefined undefined
ProductionLogger.ts:117 [CALL:[MeetingContent] Initial state:] [object Object] undefined
ProductionLogger.ts:117 [CALL:[MeetingContent] Component rendering...] undefined undefined
ProductionLogger.ts:117 [CALL:[MeetingContent] Initial state:] [object Object] undefined
console.js:654 Warning: ReferenceError: Property 'navigation' doesn't exist

This error is located at:
    at MeetingContent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:565507:101)
    at MeetingConsumer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:424568:25)
    at MeetingProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:424733:24)
    at MeetingProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:423988:41)
    at MeetingScreenSimple (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:565979:90)
    at StaticContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163293:17)
    at EnsureSingleNavigator (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:158910:24)
    at SceneView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163133:22)
    at RNSScreenContentWrapper (<anonymous>)
    at ScreenContentWrapper (<anonymous>)
    at DebugContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:493275:36)
    at RNSScreen (<anonymous>)
    at Animated(Anonymous) (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:111440:47)
    at Suspender (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492448:22)
    at Suspense (<anonymous>)
    at Freeze (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492458:23)
    at DelayedFreeze (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492414:22)
    at InnerScreen (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492209:41)
    at Screen (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492383:50)
    at ScreenStackItem (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:493155:24)
    at SceneView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:602711:21)
    at RNSScreenStack (<anonymous>)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:493132:22)
    at ScreenStack (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:493050:30)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102432:43)
    at SafeAreaProviderCompat (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:490568:24)
    at NativeStackView (http://localho
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:260
anonymous @ LogBox.js:80
reportException @ ExceptionsManager.js:111
handleException @ ExceptionsManager.js:171
onCaughtError @ ErrorHandlers.js:70
logCaughtError @ ReactFabric-dev.js:6429
runWithFiberInDEV @ ReactFabric-dev.js:571
anonymous @ ReactFabric-dev.js:6476
callCallback @ ReactFabric-dev.js:3186
commitCallbacks @ ReactFabric-dev.js:3206
runWithFiberInDEV @ ReactFabric-dev.js:571
commitClassCallbacks @ ReactFabric-dev.js:9746
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10120
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10048
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10125
commitLayoutEffects @ ReactFabric-dev.js:10618
commitRootImpl @ ReactFabric-dev.js:12455
commitRootWhenReady @ ReactFabric-dev.js:11705
performWorkOnRoot @ ReactFabric-dev.js:11652
performWorkOnRootViaSchedulerTask @ ReactFabric-dev.js:2807
Show 140 more frames
Show less
ProductionLogger.ts:78 [ERROR:NavigationErrorBoundary] Navigation error caught: {error: "Property 'navigation' doesn't exist", stack: "ReferenceError: Property 'navigation' doesn't exist\n    at MeetingContent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:565878:21)\n    at reactStackBottomFrame (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:11616:29)\n    at renderWithHooks (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:6582:40)\n    at updateFunctionComponent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:7812:34)\n    at beginWork (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:8310:41)\n    at runWithFiberInDEV (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:4492:24)\n    at performUnitOfWork (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:10229:97)\n    at workLoopSync (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:10123:57)\n    at renderRootSync (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:10107:21)\n    at performWorkOnRoot (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:9853:53)\n    at performWorkOnRootViaSchedulerTask (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:5737:24)", componentStack: '\n    at MeetingContent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:565507:101)\n    at MeetingConsumer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:424568:25)\n    at MeetingProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:424733:24)\n    at MeetingProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:423988:41)\n    at MeetingScreenSimple (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:565979:90)\n    at StaticContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163293:17)\n    at EnsureSingleNavigator (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:158910:24)\n    at SceneView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163133:22)\n    at RNSScreenContentWrapper (<anonymous>)\n    at ScreenContentWrapper (<anonymous>)\n    at DebugContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:493275:36)\n    at RNSScreen (<anonymous>)\n    at Animated(Anonymous) (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:111440:47)\n    at Suspender (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492448:22)\n    at Suspense (<anonymous>)\n    at Freeze (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492458:23)\n    at DelayedFreeze (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492414:22)\n    at InnerScreen (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492209:41)\n    at Screen (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492383:50)\n    at ScreenStackItem (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:493155:24)\n    at SceneView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:602711:21)\n    at RNSScreenStack (<anonymous>)\n    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:493132:22)\n    at ScreenStack (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:493050:30)\n    at RCTView (<anonymous>)\n    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102432:43)\n    at SafeAreaProviderCompat (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:490568:24)\n    at NativeStackView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:603025:22)\n    at PreventRemoveProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:159107:25)\n    at NavigationContent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163345:22)\n    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163360:27)\n    at NativeStackNavigator (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:603518:18)\n    at MainNavigator (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:141366:41)\n    at StaticContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163293:17)\n    at EnsureSingleNavigator (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:158910:24)\n    at SceneView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163133:22)\n    at RNSScreenContentWrapper (<anonymous>)\n    at ScreenContentWrapper (<anonymous>)\n    at DebugContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:493275:36)\n    at RNSScreen (<anonymous>)\n    at Animated(Anonymous) (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:111440:47)\n    at Suspender (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492448:22)\n    at Suspense (<anonymous>)\n    at Freeze (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492458:23)\n    at DelayedFreeze (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492414:22)\n    at InnerScreen (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492209:41)\n    at Screen (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:492383:50)\n    at ScreenStackItem (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:493155:24)\n    at SceneView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:602711:21)\n    at RNSScreenStack (<anonymous>)\n    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:493132:22)\n    at ScreenStack (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:493050:30)\n    at RCTView (<anonymous>)\n    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102432:43)\n    at SafeAreaProviderCompat (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:490568:24)\n    at NativeStackView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:603025:22)\n    at PreventRemoveProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:159107:25)\n    at NavigationContent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163345:22)\n    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163360:27)\n    at NativeStackNavigator (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:603518:18)\n    at ThemeProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:158947:21)\n    at EnsureSingleNavigator (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:158910:24)\n    at BaseNavigationContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:156640:28)\n    at NavigationContainerInner (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163828:30)\n    at NavigationErrorBoundary (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:608975:36)\n    at RNCSafeAreaView (<anonymous>)\n    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:74222:21)\n    at CssInterop.unknown (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24391:79)\n    at UltraFastLoader (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:607742:40)\n    at AppNavigator (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:132335:94)\n    at RNGestureHandlerRootView (<anonymous>)\n    at GestureHandlerRootView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:320449:21)\n    at SidebarProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:326538:24)\n    at TabNavigatorProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:406903:24)\n    at ShortsProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:486451:24)\n    at DataProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:406951:24)\n    at ContentCreatorPremiumProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:412020:24)\n    at WalletProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:322066:24)\n    at FCMChatProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:377258:24)\n    at UserDataProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:322364:24)\n    at QueryClientProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:191992:22)\n    at EnhancedQueryProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:325512:25)\n    at AuthProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:141889:24)\n    at ThemeProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:196894:24)\n    at RCTView (<anonymous>)\n    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102432:43)\n    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24391:79)\n    at KeyboardAvoiderProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:596327:24)\n    at SafeAreaEnv (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:74320:26)\n    at RNCSafeAreaProvider (<anonymous>)\n    at SafeAreaProvider (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:74002:24)\n    at SafeAreaProviderShim (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:74347:27)\n    at RNGestureHandlerRootView (<anonymous>)\n    at GestureHandlerRootView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:320449:21)\n    at AppErrorBoundary (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:619030:36)\n    at App (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:132566:69)\n    at RCTView (<anonymous>)\n    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102432:43)\n    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24391:79)\n    at RCTView (<anonymous>)\n    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102432:43)\n    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24391:79)\n    at AppContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102323:25)\n    at Adtip(RootComponent) (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:118909:28)'} Error Component Stack:
    at NavigationErrorBoundary (NavigationErrorBoundary.tsx:19:29)
    at RNCSafeAreaView (<anonymous>)
    at anonymous (SafeAreaView.tsx:24:11)
    at CssInterop.unknown (api.js:32:48)
    at UltraFastLoader (UltraFastLoader.tsx:67:27)
    at AppNavigator (App.tsx:106:59)
    at RNGestureHandlerRootView (<anonymous>)
    at GestureHandlerRootView (GestureHandlerRootView.android.tsx:12:8)
    at SidebarProvider (SidebarContext.tsx:18:11)
    at TabNavigatorProvider (TabNavigatorContext.tsx:16:84)
    at ShortsProvider (ShortsContext.tsx:24:73)
    at DataProvider (DataProvider.tsx:16:69)
    at ContentCreatorPremiumProvider (ContentCreatorPremiumContext.tsx:35:103)
    at WalletProvider (WalletContext.tsx:36:72)
    at FCMChatProvider (FCMChatContext.tsx:59:75)
    at UserDataProvider (UserDataContext.tsx:39:77)
    at QueryClientProvider (QueryClientProvider.js:20:9)
    at EnhancedQueryProvider (QueryProvider.tsx:60:79)
    at AuthProvider (AuthContext.tsx:89:11)
    at ThemeProvider (ThemeContext.tsx:22:11)
    at RCTView (<anonymous>)
    at View (View.js:32:34)
    at CssInterop.View (api.js:32:48)
    at KeyboardAvoiderProvider (KeyboardAvoiderProvider.tsx:7:13)
    at SafeAreaEnv (react-native-safe-area-context.native.js:17:36)
    at RNCSafeAreaProvider (<anonymous>)
    at SafeAreaProvider (SafeAreaContext.tsx:35:11)
    at SafeAreaProviderShim (react-native-safe-area-context.native.js:41:52)
    at RNGestureHandlerRootView (<anonymous>)
    at GestureHandlerRootView (GestureHandlerRootView.android.tsx:12:8)
    at AppErrorBoundary (AppErrorBoundary.tsx:19:29)
    at App (App.tsx:308:48)
    at RCTView (<anonymous>)
    at View (View.js:32:34)
    at CssInterop.View (api.js:32:48)
    at RCTView (<anonymous>)
    at View (View.js:32:34)
    at CssInterop.View (api.js:32:48)
    at AppContainer (AppContainer-dev.js:88:11)
    at Adtip(RootComponent) (getCachedComponentWithDebugName.js:26:42)
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
error @ ProductionLogger.ts:78
componentDidCatch @ NavigationErrorBoundary.tsx:39
reactStackBottomFrame @ ReactFabric-dev.js:14821
anonymous @ ReactFabric-dev.js:6487
callCallback @ ReactFabric-dev.js:3186
commitCallbacks @ ReactFabric-dev.js:3206
runWithFiberInDEV @ ReactFabric-dev.js:571
commitClassCallbacks @ ReactFabric-dev.js:9746
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10120
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10048
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10160
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10223
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10043
recursivelyTraverseLayoutEffects @ ReactFabric-dev.js:10624
commitLayoutEffectOnFiber @ ReactFabric-dev.js:10125
commitLayoutEffects @ ReactFabric-dev.js:10618
commitRootImpl @ ReactFabric-dev.js:12455
commitRootWhenReady @ ReactFabric-dev.js:11705
performWorkOnRoot @ ReactFabric-dev.js:11652
performWorkOnRootViaSchedulerTask @ ReactFabric-dev.js:2807
Show 136 more frames
Show less
ProductionLogger.ts:117 [CALL:[MeetingScreenSimple] Component cleanup for session:] dffe0741-d37d-4934-bb68-97eb19b4983d Component ID:
ProductionLogger.ts:117 [CALL:[MeetingScreenSimple] Cleaned up global component registration] undefined undefined
ApiService.ts:253 Authorization header added to request for: /api/get-fcm-token/unknown
ApiService.ts:260 🚀 API REQUEST: {method: 'GET', url: '/api/get-fcm-token/unknown', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/get-fcm-token/unknown', headers: {…}, params: undefined, data: undefined, timeout: 60000, timestamp: '2025-07-31T08:19:35.238Z'}
AppOpenAdManager.ts:170 App state changed: background -> background
AppOpenAdManager.ts:210 App went to background, resetting session flag
ReliableCallManager.ts:261 [ReliableCallManager] App state changed to: background
ApiService.ts:303 ❌ API ERROR RESPONSE: {method: 'GET', url: '/api/get-fcm-token/unknown', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/get-fcm-token/unknown', status: 404, statusText: undefined, headers: {…}, data: {…}, timestamp: '2025-07-31T08:19:35.383Z'}
ApiService.ts:633 API Error Details (handleError): {isAxiosError: true, status: 404, statusText: undefined, data: {…}, message: 'Request failed with status code 404', config: {…}}
ApiService.ts:1089 [ApiService] Error fetching FCM token for user unknown: Error: User not found
    at handleError (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:143521:29)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:143358:35)
    at throw (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1426:19)
    at _throw (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1443:29)
    at tryCallOne (address at InternalBytecode.js:1:1180)
    at anonymous (address at InternalBytecode.js:1:1874)
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ ApiService.ts:1089
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 8 more frames
Show less
ApiService.ts:1597 [ApiService] sendCallSignal error: Error: No FCM token found for recipient: unknown
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:144576:30)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1426:19)
    at _next (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1440:29)
    at tryCallOne (address at InternalBytecode.js:1:1180)
    at anonymous (address at InternalBytecode.js:1:1874)
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ ApiService.ts:1597
asyncGeneratorStep @ asyncToGenerator.js:3
_next @ asyncToGenerator.js:17
Show 8 more frames
Show less
CallSignalingService.ts:158 [CallSignalingService] sendSignal error Error: No FCM token found for recipient: unknown
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:144576:30)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1426:19)
    at _next (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1440:29)
    at tryCallOne (address at InternalBytecode.js:1:1180)
    at anonymous (address at InternalBytecode.js:1:1874)
anonymous @ console.js:654
overrideMethod @ backend.js:17042
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:84
?anon_0_ @ CallSignalingService.ts:158
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 7 more frames
Show less
CallSignalingService.ts:177 [CallSignalingService] CALL_ACCEPT signal sent successfully
ProductionLogger.ts:117 [CALL:CallController] Starting payment tracking for accepted voice call undefined
ApiService.ts:246 Request to protected endpoint: /api/voice-call. Attempting to add Authorization header.
ApiService.ts:253 Authorization header added to request for: /api/voice-call
ApiService.ts:260 🚀 API REQUEST: {method: 'POST', url: '/api/voice-call', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/voice-call', headers: {…}, params: undefined, data: {…}, timeout: 60000, timestamp: '2025-07-31T08:19:35.427Z'}
ApiService.ts:303 ❌ API ERROR RESPONSE: {method: 'POST', url: '/api/voice-call', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/voice-call', status: 400, statusText: undefined, headers: {…}, data: {…}, timestamp: '2025-07-31T08:19:35.502Z'}
ApiService.ts:633 API Error Details (handleError): {isAxiosError: true, status: 400, statusText: undefined, data: {…}, message: 'Request failed with status code 400', config: {…}}
ProductionLogger.ts:78 [ERROR:CallController] Failed to start payment tracking for accepted call Error: Missing required parameters: callerId, receiverId, action
    at handleError (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:143521:29)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:143382:35)
    at throw (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1426:19)
    at _throw (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1443:29)
    at tryCallOne (address at InternalBytecode.js:1:1180)
    at anonymous (address at InternalBytecode.js:1:1874)
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
error @ ProductionLogger.ts:78
logError @ ProductionLogger.ts:163
?anon_0_ @ CallController.ts:761
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 8 more frames
Show less
ApiService.ts:1071 [ApiService] Getting FCM token for single user: 63779
ApiService.ts:246 Request to protected endpoint: /api/get-fcm-token/63779. Attempting to add Authorization header.
ApiService.ts:253 Authorization header added to request for: /api/get-fcm-token/63779
ApiService.ts:260 🚀 API REQUEST: {method: 'GET', url: '/api/get-fcm-token/63779', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/get-fcm-token/63779', headers: {…}, params: undefined, data: undefined, timeout: 60000, timestamp: '2025-07-31T08:19:35.531Z'}
ProductionLogger.ts:96 [NET:ApiService] GET http://*************:7082/api/get-fcm-token/63779 (200) {statusText: undefined, headers: {…}, data: {…}, timestamp: '2025-07-31T08:19:35.671Z'}
ApiService.ts:1074 [ApiService] Single user FCM token response: {status: true, fcm_token: 'eLJnReg_Sy2CY5RBgv81LC:APA91bG3mSbnH34z47GPhZzlN5aPadNAhhorsMf_5Mv1wnLTl_y7PFi5cDFcJWhgAwdobKXO6-uWYEjo4ax5c5EHI0svSQrRB8MEbAPiNmPMx-G6lQQGUpI'}
ApiService.ts:1077 [ApiService] Successfully extracted FCM token for user 63779: eLJnReg_Sy2CY5RBgv81LC:APA91bG3mSbnH34z47GPhZzlN5aPadNAhhorsMf_5Mv1wnLTl_y7PFi5cDFcJWhgAwdobKXO6-uWYEjo4ax5c5EHI0svSQrRB8MEbAPiNmPMx-G6lQQGUpI
ApiService.ts:1607 🚀 [ApiService] Making direct call to FCM Server for update-call: https://us-central1-adtip-3873c.cloudfunctions.net/callApi
ApiService.ts:1650 📤 [ApiService] updateCallStatus validated payload: {
  "callerInfo": {
    "token": "eLJnReg_Sy2CY5RBgv81LC:APA91bG3mSbnH34z47GPhZzlN5aPadNAhhorsMf_5Mv1wnLTl_y7PFi5cDFcJWhgAwdobKXO6-uWYEjo4ax5c5EHI0svSQrRB8MEbAPiNmPMx-G6lQQGUpI",
    "name": "R17BKP",
    "platform": "ANDROID"
  },
  "type": "CALL_ACCEPTED"
}
ApiService.ts:1654 🔑 [ApiService] Auth token available: true
ApiService.ts:1667 🚀 [ApiService] Request headers: {Content-Type: 'application/json', Accept: 'application/json', Authorization: 'Bearer [REDACTED]'}
AppOpenAdManager.ts:170 App state changed: background -> active
AppOpenAdManager.ts:183 [AppOpenAdManager] Suppressing ad because a call is active.
ReliableCallManager.ts:261 [ReliableCallManager] App state changed to: active
console.js:654 Bluetooth Connect Permission Granted
ApiService.ts:1678 ✅ [ApiService] update-call response: {messageId: 'projects/adtip-3873c/messages/0:1753949976279211%e9e87daaf9fd7ecd'}
ProductionLogger.ts:117 [CALL:CallController] Status changed: connecting -> in_call undefined
ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
useFCMMessageRouter.ts:39 [useFCMMessageRouter] FCM message received, app state: active
useFCMMessageRouter.ts:43 [useFCMMessageRouter] Processing foreground FCM message: {info: '{"callerInfo":{"token":"eLJnReg_Sy2CY5RBgv81LC:APA91bG3mSbnH34z47GPhZzlN5aPadNAhhorsMf_5Mv1wnLTl_y7PFi5cDFcJWhgAwdobKXO6-uWYEjo4ax5c5EHI0svSQrRB8MEbAPiNmPMx-G6lQQGUpI","name":"R17BKP","platform":"ANDROID"},"type":"CALL_ACCEPTED"}'}
LogUtils.ts:44 [FCMMessageRouter] Routing foreground message: {data: {…}, notification: undefined}
LogUtils.ts:44 [FCMMessageRouter] Detected message type: CALL_ACCEPTED
FCMMessageRouter.ts:137 [FCMMessageRouter] Routing to enhanced call handler...
CallFCMHandler.ts:44 [CallFCMHandler] Processing call message: {context: 'foreground', messageType: 'CALL_ACCEPTED', data: {…}}
CallFCMHandler.ts:146 [CallFCMHandler] Call accepted by remote party
CallFCMHandler.ts:189 [CallFCMHandler] Navigating to call: adtip://call/active/undefined/undefined/undefined?callerName=R17BKP&callType=video
FCMMessageRouter.ts:161 [FCMMessageRouter] Enhanced call message processed successfully
useFCMMessageRouter.ts:45 [useFCMMessageRouter] Foreground message routed successfully
AppOpenAdManager.ts:170 App state changed: background -> background
AppOpenAdManager.ts:210 App went to background, resetting session flag
ReliableCallManager.ts:261 [ReliableCallManager] App state changed to: background
ProductionLogger.ts:51 [DEBUG:SimplifiedDeepLinkService] Handling deep link: adtip://call/active/undefined/undefined/undefined?callerName=R17BKP&callType=video
ProductionLogger.ts:69 [WARN:SimplifiedDeepLinkService] Navigation not ready, storing link for later
anonymous @ console.js:654
overrideMethod @ backend.js:17042
anonymous @ setUpDeveloperTools.js:40
registerWarning @ LogBox.js:171
anonymous @ LogBox.js:84
warn @ ProductionLogger.ts:69
navigateToRoute @ SimplifiedDeepLinkService.ts:133
handleDeepLink @ SimplifiedDeepLinkService.ts:98
anonymous @ SimplifiedDeepLinkService.ts:39
emit @ EventEmitter.js:126
anonymous @ RCTDeviceEventEmitter.js:14
emit @ RCTDeviceEventEmitter.js:33
Show 8 more frames
Show less
ProductionLogger.ts:51 [DEBUG:NavigationAnalytics] Deep link tracked: {url: 'adtip://call/active/undefined/undefined/undefined?callerName=R17BKP&callType=video', screen: 'Home', success: true}
AppOpenAdManager.ts:170 App state changed: background -> active
AppOpenAdManager.ts:183 [AppOpenAdManager] Suppressing ad because a call is active.
ReliableCallManager.ts:261 [ReliableCallManager] App state changed to: active
NotificationPersistenceService.ts:189 [NotificationPersistence] Call timeout reached: dffe0741-d37d-4934-bb68-97eb19b4983d
NotificationPersistenceService.ts:84 [NotificationPersistence] Removing pending call: dffe0741-d37d-4934-bb68-97eb19b4983d